pip install together pandas matplotlib seaborn plotly openpyxl python-docx PyPDF2 pillow numpy scikit-learn wordcloud

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

import docx
import PyPDF2
from PIL import Image
import json
import re
from io import StringIO

import together
from together import Together

plt.style.use('default')
sns.set_palette("husl")
%matplotlib inline

TOGETHER_API_KEY = "cdd020e6a49733d3836952dbd379ccc3476264c76c0a510fcd9a6b490b588588"
MODEL_NAME = "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8"

client = Together(api_key=TOGETHER_API_KEY)

print("✅ Data Analyst Agent initialized!")
print(f"📊 Using model: {MODEL_NAME}")

class DataAnalystAgent:
    def __init__(self, api_key, model_name):
        self.client = Together(api_key=api_key)
        self.model_name = model_name
        self.current_data = None
        self.data_info = {}
        self.conversation_history = []
        
    def query_llm(self, prompt, system_message="You are a senior data analyst with over 15 years of experience in advanced analytics, statistical modeling, and business intelligence across multiple industries. Your role is to thoroughly analyze the provided dataset, uncover meaningful patterns, trends, and anomalies, and generate actionable, descriptive insights that can drive strategic decision-making. For each insight, provide clear explanations, relevant statistical measures, and, where appropriate, suggest advanced data visualization techniques to communicate findings effectively to both technical and non-technical stakeholders. Anticipate potential business questions, highlight key opportunities or risks, and recommend next steps or further analyses. If any information or data context is missing, ask clarifying questions before proceeding. Present your findings in a structured, comprehensive, and business-oriented manner."):
        """Query the Llama model with a prompt"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2048,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"Error querying model: {str(e)}"
    
    def read_file(self, file_path):
        """Read various file formats"""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        try:
            if file_extension == '.csv':
                return pd.read_csv(file_path)
            elif file_extension in ['.xlsx', '.xls']:
                return pd.read_excel(file_path)
            elif file_extension == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return content
            elif file_extension == '.docx':
                doc = docx.Document(file_path)
                content = '\n'.join([paragraph.text for paragraph in doc.paragraphs])
                return content
            elif file_extension == '.pdf':
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    content = ''
                    for page in reader.pages:
                        content += page.extract_text() + '\n'
                return content
            elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
                img = Image.open(file_path)
                return f"Image loaded: {img.size} pixels, Mode: {img.mode}"
            else:
                return f"Unsupported file format: {file_extension}"
        except Exception as e:
            return f"Error reading file: {str(e)}"
    
    def analyze_data(self, data, question=None):
        """Analyze data and provide insights"""
        if isinstance(data, pd.DataFrame):
            # Generate data summary
            summary = self.generate_data_summary(data)
            
            if question:
                prompt = f"""
                I have a dataset with the following summary:
                {summary}
                
                Dataset preview:
                {data.head().to_string()}
                
                Question: {question}
                
                Please provide a detailed analysis and answer to this question based on the data.
                """
            else:
                prompt = f"""
                I have a dataset with the following summary:
                {summary}
                
                Dataset preview:
                {data.head().to_string()}
                
                Please provide a comprehensive analysis of this dataset including:
                1. Key insights and patterns
                2. Data quality observations
                3. Potential areas for further investigation
                4. Suggested visualizations
                """
            
            system_message = """You are an expert data analyst. Provide clear, actionable insights 
            based on the data provided. Focus on practical findings and recommendations."""
            
            return self.query_llm(prompt, system_message)
        
        elif isinstance(data, str):
            # Text analysis
            if question:
                prompt = f"""
                Text content: {data[:2000]}...
                
                Question: {question}
                
                Please analyze this text and answer the question.
                """
            else:
                prompt = f"""
                Text content: {data[:2000]}...
                
                Please provide a comprehensive analysis of this text including:
                1. Key themes and topics
                2. Summary of main points
                3. Any patterns or insights
                """
            
            return self.query_llm(prompt)
    
    def generate_data_summary(self, df):
        """Generate a comprehensive summary of the dataset"""
        summary = f"""
        Dataset Shape: {df.shape[0]} rows, {df.shape[1]} columns
        
        Columns and Data Types:
        {df.dtypes.to_string()}
        
        Missing Values:
        {df.isnull().sum().to_string()}
        
        Numerical Columns Summary:
        {df.describe().to_string() if len(df.select_dtypes(include=[np.number]).columns) > 0 else 'No numerical columns'}
        
        Categorical Columns:
        {list(df.select_dtypes(include=['object']).columns)}
        """
        return summary

    def create_visualizations(self, df, chart_type='auto', columns=None):
        """Create various visualizations based on data type"""
        if not isinstance(df, pd.DataFrame):
            return "Visualizations are only available for structured data (CSV, Excel files)"
        
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
        
        if chart_type == 'auto':
            # Auto-generate appropriate visualizations
            plots = []
            
            # 1. Data overview
            if len(numerical_cols) > 0:
                fig, axes = plt.subplots(2, 2, figsize=(15, 10))
                fig.suptitle('Data Overview', fontsize=16)
                
                # Distribution of first numerical column
                if len(numerical_cols) >= 1:
                    axes[0,0].hist(df[numerical_cols[0]].dropna(), bins=30, alpha=0.7)
                    axes[0,0].set_title(f'Distribution of {numerical_cols[0]}')
                    axes[0,0].set_xlabel(numerical_cols[0])
                    axes[0,0].set_ylabel('Frequency')
                
                # Correlation heatmap if multiple numerical columns
                if len(numerical_cols) > 1:
                    corr_matrix = df[numerical_cols].corr()
                    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[0,1])
                    axes[0,1].set_title('Correlation Matrix')
                
                # Missing values visualization
                missing_data = df.isnull().sum()
                missing_data = missing_data[missing_data > 0]
                if len(missing_data) > 0:
                    axes[1,0].bar(range(len(missing_data)), missing_data.values)
                    axes[1,0].set_xticks(range(len(missing_data)))
                    axes[1,0].set_xticklabels(missing_data.index, rotation=45)
                    axes[1,0].set_title('Missing Values by Column')
                    axes[1,0].set_ylabel('Count')
                
                # Data types pie chart
                type_counts = df.dtypes.value_counts()
                axes[1,1].pie(type_counts.values, labels=type_counts.index, autopct='%1.1f%%')
                axes[1,1].set_title('Data Types Distribution')
                
                plt.tight_layout()
                plt.show()
                plots.append('Data Overview Dashboard')
            
            return plots
        
        return "Custom visualization types not implemented yet"
    
    def upload_and_analyze(self, file_path, question=None):
        """Main method to upload file and analyze"""
        print(f"📁 Loading file: {file_path}")
        
        # Read the file
        data = self.read_file(file_path)
        
        if isinstance(data, str) and data.startswith('Error'):
            return data
        
        self.current_data = data
        
        # Analyze the data
        analysis = self.analyze_data(data, question)
        
        # Create visualizations if it's structured data
        if isinstance(data, pd.DataFrame):
            print("\n📊 Creating visualizations...")
            self.create_visualizations(data)
        
        return analysis
    
    def ask_followup(self, question):
        """Ask follow-up questions about the current data"""
        if self.current_data is None:
            return "Please upload a file first using upload_and_analyze()"
        
        return self.analyze_data(self.current_data, question)

# Initialize the agent
agent = DataAnalystAgent(TOGETHER_API_KEY, MODEL_NAME)
print("🤖 Data Analyst Agent ready!")

# Example: Analyze a CSV file
# Replace 'your_file.csv' with the actual path to your file
file_path = "your_file.csv"  # Change this to your file path

# Basic analysis
result = agent.upload_and_analyze(file_path)
print("Analysis Result:")
print(result)

# Example: Analyze with a specific question
question = "What are the main trends in this data?"
result = agent.upload_and_analyze(file_path, question)
print(f"Answer to '{question}':")
print(result)

# Ask follow-up questions about the loaded data
followup_question = "Can you identify any outliers in the data?"
followup_result = agent.ask_followup(followup_question)
print(f"Follow-up Answer:")
print(followup_result)

# Create sample data for testing
import random
from datetime import datetime, timedelta

# Generate sample sales data
np.random.seed(42)
n_records = 1000

sample_data = {
    'date': [datetime.now() - timedelta(days=x) for x in range(n_records)],
    'product': np.random.choice(['Product A', 'Product B', 'Product C', 'Product D'], n_records),
    'sales': np.random.normal(1000, 200, n_records),
    'region': np.random.choice(['North', 'South', 'East', 'West'], n_records),
    'customer_satisfaction': np.random.uniform(1, 5, n_records)
}

sample_df = pd.DataFrame(sample_data)
sample_df['sales'] = sample_df['sales'].round(2)
sample_df['customer_satisfaction'] = sample_df['customer_satisfaction'].round(1)

# Save sample data
sample_df.to_csv('sample_sales_data.csv', index=False)
print("✅ Sample data created: sample_sales_data.csv")
print(sample_df.head())

# Test with sample data
print("🧪 Testing with sample data...")
result = agent.upload_and_analyze('sample_sales_data.csv')
print("\nAnalysis Result:")
print(result)

def interactive_analysis():
    """Interactive function for file analysis"""
    print("🔍 Interactive Data Analysis")
    print("Supported formats: .csv, .xlsx, .txt, .docx, .pdf, images")
    
    file_path = input("Enter file path: ")
    
    if not os.path.exists(file_path):
        print("❌ File not found!")
        return
    
    question = input("Enter your question (or press Enter for general analysis): ")
    question = question.strip() if question.strip() else None
    
    print("\n🔄 Analyzing...")
    result = agent.upload_and_analyze(file_path, question)
    print("\n📋 Analysis Result:")
    print(result)
    
    # Follow-up questions loop
    while True:
        followup = input("\nAsk a follow-up question (or 'quit' to exit): ")
        if followup.lower() in ['quit', 'exit', 'q']:
            break
        
        if followup.strip():
            followup_result = agent.ask_followup(followup)
            print("\n📋 Follow-up Answer:")
            print(followup_result)

# Uncomment the line below to run interactive analysis
# interactive_analysis()

def create_advanced_plots(df, plot_type='correlation'):
    """Create advanced visualizations"""
    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
    
    if plot_type == 'correlation' and len(numerical_cols) > 1:
        # Interactive correlation plot with Plotly
        corr_matrix = df[numerical_cols].corr()
        fig = px.imshow(corr_matrix, 
                       text_auto=True, 
                       aspect="auto",
                       title="Interactive Correlation Matrix")
        fig.show()
    
    elif plot_type == 'distribution' and len(numerical_cols) > 0:
        # Distribution plots for all numerical columns
        fig = make_subplots(rows=len(numerical_cols), cols=1,
                           subplot_titles=numerical_cols)
        
        for i, col in enumerate(numerical_cols):
            fig.add_trace(go.Histogram(x=df[col], name=col), row=i+1, col=1)
        
        fig.update_layout(height=300*len(numerical_cols), 
                         title_text="Distribution of Numerical Variables")
        fig.show()
    
    elif plot_type == 'scatter' and len(numerical_cols) >= 2:
        # Scatter plot matrix
        fig = px.scatter_matrix(df[numerical_cols], 
                               title="Scatter Plot Matrix")
        fig.show()
    
    else:
        print(f"Plot type '{plot_type}' not available or insufficient data")

# Example usage with sample data
if 'sample_df' in locals():
    print("📊 Creating advanced visualizations...")
    create_advanced_plots(sample_df, 'correlation')

def export_analysis(analysis_text, filename='analysis_report.txt'):
    """Export analysis results to a file"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"Data Analysis Report\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Model: {MODEL_NAME}\n")
        f.write("="*50 + "\n\n")
        f.write(analysis_text)
    
    print(f"✅ Analysis exported to {filename}")

# Example: Export the last analysis
# export_analysis(result, 'my_analysis_report.txt')