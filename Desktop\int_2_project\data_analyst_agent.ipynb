{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Analyst Agent\n", "## Powered by <PERSON><PERSON><PERSON>-4-<PERSON><PERSON><PERSON>-17B-128E-Instruct-FP8 from Together.ai\n", "\n", "This notebook implements a comprehensive data analyst agent that can:\n", "- Upload and process various file formats (.doc, .txt, .xlsx, .csv, .pdf, images)\n", "- Analyze data and answer questions\n", "- Create visualizations\n", "- Handle follow-up questions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install together pandas mat<PERSON><PERSON><PERSON>b seaborn plotly openpyxl python-docx PyPDF2 pillow numpy scikit-learn wordcloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# File processing imports\n", "import docx\n", "import PyPDF2\n", "from PIL import Image\n", "import json\n", "import re\n", "from io import StringIO\n", "\n", "# Together AI import\n", "import together\n", "from together import Together\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "TOGETHER_API_KEY = \"your_together_api_key_here\"  # Replace with your actual API key\n", "MODEL_NAME = \"meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8\"\n", "\n", "# Initialize Together client\n", "client = Together(api_key=TOGETHER_API_KEY)\n", "\n", "print(\"✅ Data Analyst Agent initialized!\")\n", "print(f\"📊 Using model: {MODEL_NAME}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class DataAnalystAgent:\n", "    def __init__(self, api_key, model_name):\n", "        self.client = Together(api_key=api_key)\n", "        self.model_name = model_name\n", "        self.current_data = None\n", "        self.data_info = {}\n", "        self.conversation_history = []\n", "        \n", "    def query_llm(self, prompt, system_message=\"You are a helpful data analyst assistant.\"):\n", "        \"\"\"Query the Llama model with a prompt\"\"\"\n", "        try:\n", "            response = self.client.chat.completions.create(\n", "                model=self.model_name,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_message},\n", "                    {\"role\": \"user\", \"content\": prompt}\n", "                ],\n", "                max_tokens=2048,\n", "                temperature=0.7\n", "            )\n", "            return response.choices[0].message.content\n", "        except Exception as e:\n", "            return f\"Error querying model: {str(e)}\"\n", "    \n", "    def read_file(self, file_path):\n", "        \"\"\"Read various file formats\"\"\"\n", "        file_extension = os.path.splitext(file_path)[1].lower()\n", "        \n", "        try:\n", "            if file_extension == '.csv':\n", "                return pd.read_csv(file_path)\n", "            elif file_extension in ['.xlsx', '.xls']:\n", "                return pd.read_excel(file_path)\n", "            elif file_extension == '.txt':\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    content = f.read()\n", "                return content\n", "            elif file_extension == '.docx':\n", "                doc = docx.Document(file_path)\n", "                content = '\\n'.join([paragraph.text for paragraph in doc.paragraphs])\n", "                return content\n", "            elif file_extension == '.pdf':\n", "                with open(file_path, 'rb') as f:\n", "                    reader = PyPDF2.PdfReader(f)\n", "                    content = ''\n", "                    for page in reader.pages:\n", "                        content += page.extract_text() + '\\n'\n", "                return content\n", "            elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:\n", "                img = Image.open(file_path)\n", "                return f\"Image loaded: {img.size} pixels, Mode: {img.mode}\"\n", "            else:\n", "                return f\"Unsupported file format: {file_extension}\"\n", "        except Exception as e:\n", "            return f\"Error reading file: {str(e)}\"\n", "    \n", "    def analyze_data(self, data, question=None):\n", "        \"\"\"Analyze data and provide insights\"\"\"\n", "        if isinstance(data, pd.DataFrame):\n", "            # Generate data summary\n", "            summary = self.generate_data_summary(data)\n", "            \n", "            if question:\n", "                prompt = f\"\"\"\n", "                I have a dataset with the following summary:\n", "                {summary}\n", "                \n", "                Dataset preview:\n", "                {data.head().to_string()}\n", "                \n", "                Question: {question}\n", "                \n", "                Please provide a detailed analysis and answer to this question based on the data.\n", "                \"\"\"\n", "            else:\n", "                prompt = f\"\"\"\n", "                I have a dataset with the following summary:\n", "                {summary}\n", "                \n", "                Dataset preview:\n", "                {data.head().to_string()}\n", "                \n", "                Please provide a comprehensive analysis of this dataset including:\n", "                1. Key insights and patterns\n", "                2. Data quality observations\n", "                3. Potential areas for further investigation\n", "                4. Suggested visualizations\n", "                \"\"\"\n", "            \n", "            system_message = \"\"\"You are an expert data analyst. Provide clear, actionable insights \n", "            based on the data provided. Focus on practical findings and recommendations.\"\"\"\n", "            \n", "            return self.query_llm(prompt, system_message)\n", "        \n", "        elif isinstance(data, str):\n", "            # Text analysis\n", "            if question:\n", "                prompt = f\"\"\"\n", "                Text content: {data[:2000]}...\n", "                \n", "                Question: {question}\n", "                \n", "                Please analyze this text and answer the question.\n", "                \"\"\"\n", "            else:\n", "                prompt = f\"\"\"\n", "                Text content: {data[:2000]}...\n", "                \n", "                Please provide a comprehensive analysis of this text including:\n", "                1. Key themes and topics\n", "                2. Summary of main points\n", "                3. Any patterns or insights\n", "                \"\"\"\n", "            \n", "            return self.query_llm(prompt)\n", "    \n", "    def generate_data_summary(self, df):\n", "        \"\"\"Generate a comprehensive summary of the dataset\"\"\"\n", "        summary = f\"\"\"\n", "        Dataset Shape: {df.shape[0]} rows, {df.shape[1]} columns\n", "        \n", "        Columns and Data Types:\n", "        {df.dtypes.to_string()}\n", "        \n", "        Missing Values:\n", "        {df.isnull().sum().to_string()}\n", "        \n", "        Numerical Columns Summary:\n", "        {df.describe().to_string() if len(df.select_dtypes(include=[np.number]).columns) > 0 else 'No numerical columns'}\n", "        \n", "        Categorical Columns:\n", "        {list(df.select_dtypes(include=['object']).columns)}\n", "        \"\"\"\n", "        return summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    def create_visualizations(self, df, chart_type='auto', columns=None):\n", "        \"\"\"Create various visualizations based on data type\"\"\"\n", "        if not isinstance(df, pd.DataFrame):\n", "            return \"Visualizations are only available for structured data (CSV, Excel files)\"\n", "        \n", "        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()\n", "        \n", "        if chart_type == 'auto':\n", "            # Auto-generate appropriate visualizations\n", "            plots = []\n", "            \n", "            # 1. Data overview\n", "            if len(numerical_cols) > 0:\n", "                fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "                fig.suptitle('Data Overview', fontsize=16)\n", "                \n", "                # Distribution of first numerical column\n", "                if len(numerical_cols) >= 1:\n", "                    axes[0,0].hist(df[numerical_cols[0]].dropna(), bins=30, alpha=0.7)\n", "                    axes[0,0].set_title(f'Distribution of {numerical_cols[0]}')\n", "                    axes[0,0].set_xlabel(numerical_cols[0])\n", "                    axes[0,0].set_ylabel('Frequency')\n", "                \n", "                # Correlation heatmap if multiple numerical columns\n", "                if len(numerical_cols) > 1:\n", "                    corr_matrix = df[numerical_cols].corr()\n", "                    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[0,1])\n", "                    axes[0,1].set_title('Correlation Matrix')\n", "                \n", "                # Missing values visualization\n", "                missing_data = df.isnull().sum()\n", "                missing_data = missing_data[missing_data > 0]\n", "                if len(missing_data) > 0:\n", "                    axes[1,0].bar(range(len(missing_data)), missing_data.values)\n", "                    axes[1,0].set_xticks(range(len(missing_data)))\n", "                    axes[1,0].set_xticklabels(missing_data.index, rotation=45)\n", "                    axes[1,0].set_title('Missing Values by Column')\n", "                    axes[1,0].set_ylabel('Count')\n", "                \n", "                # Data types pie chart\n", "                type_counts = df.dtypes.value_counts()\n", "                axes[1,1].pie(type_counts.values, labels=type_counts.index, autopct='%1.1f%%')\n", "                axes[1,1].set_title('Data Types Distribution')\n", "                \n", "                plt.tight_layout()\n", "                plt.show()\n", "                plots.append('Data Overview Dashboard')\n", "            \n", "            return plots\n", "        \n", "        return \"Custom visualization types not implemented yet\"\n", "    \n", "    def upload_and_analyze(self, file_path, question=None):\n", "        \"\"\"Main method to upload file and analyze\"\"\"\n", "        print(f\"📁 Loading file: {file_path}\")\n", "        \n", "        # Read the file\n", "        data = self.read_file(file_path)\n", "        \n", "        if isinstance(data, str) and data.startswith('Error'):\n", "            return data\n", "        \n", "        self.current_data = data\n", "        \n", "        # Analyze the data\n", "        analysis = self.analyze_data(data, question)\n", "        \n", "        # Create visualizations if it's structured data\n", "        if isinstance(data, pd.DataFrame):\n", "            print(\"\\n📊 Creating visualizations...\")\n", "            self.create_visualizations(data)\n", "        \n", "        return analysis\n", "    \n", "    def ask_followup(self, question):\n", "        \"\"\"Ask follow-up questions about the current data\"\"\"\n", "        if self.current_data is None:\n", "            return \"Please upload a file first using upload_and_analyze()\"\n", "        \n", "        return self.analyze_data(self.current_data, question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the agent\n", "agent = DataAnalystAgent(TOGETHER_API_KEY, MODEL_NAME)\n", "print(\"🤖 Data Analyst Agent ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage Examples\n", "\n", "### 1. Upload and Analyze a File"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Analyze a CSV file\n", "# Replace 'your_file.csv' with the actual path to your file\n", "file_path = \"your_file.csv\"  # Change this to your file path\n", "\n", "# Basic analysis\n", "result = agent.upload_and_analyze(file_path)\n", "print(\"Analysis Result:\")\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Analyze with a specific question\n", "question = \"What are the main trends in this data?\"\n", "result = agent.upload_and_analyze(file_path, question)\n", "print(f\"Answer to '{question}':\")\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Ask Follow-up Questions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ask follow-up questions about the loaded data\n", "followup_question = \"Can you identify any outliers in the data?\"\n", "followup_result = agent.ask_followup(followup_question)\n", "print(f\"Follow-up Answer:\")\n", "print(followup_result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. <PERSON><PERSON> Sample Data for Testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample data for testing\n", "import random\n", "from datetime import datetime, timedelta\n", "\n", "# Generate sample sales data\n", "np.random.seed(42)\n", "n_records = 1000\n", "\n", "sample_data = {\n", "    'date': [datetime.now() - timedelta(days=x) for x in range(n_records)],\n", "    'product': np.random.choice(['Product A', 'Product B', 'Product C', 'Product D'], n_records),\n", "    'sales': np.random.normal(1000, 200, n_records),\n", "    'region': np.random.choice(['North', 'South', 'East', 'West'], n_records),\n", "    'customer_satisfaction': np.random.uniform(1, 5, n_records)\n", "}\n", "\n", "sample_df = pd.DataFrame(sample_data)\n", "sample_df['sales'] = sample_df['sales'].round(2)\n", "sample_df['customer_satisfaction'] = sample_df['customer_satisfaction'].round(1)\n", "\n", "# Save sample data\n", "sample_df.to_csv('sample_sales_data.csv', index=False)\n", "print(\"✅ Sample data created: sample_sales_data.csv\")\n", "print(sample_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with sample data\n", "print(\"🧪 Testing with sample data...\")\n", "result = agent.upload_and_analyze('sample_sales_data.csv')\n", "print(\"\\nAnalysis Result:\")\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. Interactive File Upload Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def interactive_analysis():\n", "    \"\"\"Interactive function for file analysis\"\"\"\n", "    print(\"🔍 Interactive Data Analysis\")\n", "    print(\"Supported formats: .csv, .xlsx, .txt, .docx, .pdf, images\")\n", "    \n", "    file_path = input(\"Enter file path: \")\n", "    \n", "    if not os.path.exists(file_path):\n", "        print(\"❌ File not found!\")\n", "        return\n", "    \n", "    question = input(\"Enter your question (or press Enter for general analysis): \")\n", "    question = question.strip() if question.strip() else None\n", "    \n", "    print(\"\\n🔄 Analyzing...\")\n", "    result = agent.upload_and_analyze(file_path, question)\n", "    print(\"\\n📋 Analysis Result:\")\n", "    print(result)\n", "    \n", "    # Follow-up questions loop\n", "    while True:\n", "        followup = input(\"\\nAsk a follow-up question (or 'quit' to exit): \")\n", "        if followup.lower() in ['quit', 'exit', 'q']:\n", "            break\n", "        \n", "        if followup.strip():\n", "            followup_result = agent.ask_followup(followup)\n", "            print(\"\\n📋 Follow-up Answer:\")\n", "            print(followup_result)\n", "\n", "# Uncomment the line below to run interactive analysis\n", "# interactive_analysis()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5. Advanced Visualization Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_advanced_plots(df, plot_type='correlation'):\n", "    \"\"\"Create advanced visualizations\"\"\"\n", "    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()\n", "    \n", "    if plot_type == 'correlation' and len(numerical_cols) > 1:\n", "        # Interactive correlation plot with <PERSON>lotly\n", "        corr_matrix = df[numerical_cols].corr()\n", "        fig = px.imshow(corr_matrix, \n", "                       text_auto=True, \n", "                       aspect=\"auto\",\n", "                       title=\"Interactive Correlation Matrix\")\n", "        fig.show()\n", "    \n", "    elif plot_type == 'distribution' and len(numerical_cols) > 0:\n", "        # Distribution plots for all numerical columns\n", "        fig = make_subplots(rows=len(numerical_cols), cols=1,\n", "                           subplot_titles=numerical_cols)\n", "        \n", "        for i, col in enumerate(numerical_cols):\n", "            fig.add_trace(go.Histogram(x=df[col], name=col), row=i+1, col=1)\n", "        \n", "        fig.update_layout(height=300*len(numerical_cols), \n", "                         title_text=\"Distribution of Numerical Variables\")\n", "        fig.show()\n", "    \n", "    elif plot_type == 'scatter' and len(numerical_cols) >= 2:\n", "        # Scatter plot matrix\n", "        fig = px.scatter_matrix(df[numerical_cols], \n", "                               title=\"Scatter Plot Matrix\")\n", "        fig.show()\n", "    \n", "    else:\n", "        print(f\"Plot type '{plot_type}' not available or insufficient data\")\n", "\n", "# Example usage with sample data\n", "if 'sample_df' in locals():\n", "    print(\"📊 Creating advanced visualizations...\")\n", "    create_advanced_plots(sample_df, 'correlation')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6. Export Analysis Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_analysis(analysis_text, filename='analysis_report.txt'):\n", "    \"\"\"Export analysis results to a file\"\"\"\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        f.write(f\"Data Analysis Report\\n\")\n", "        f.write(f\"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "        f.write(f\"Model: {MODEL_NAME}\\n\")\n", "        f.write(\"=\"*50 + \"\\n\\n\")\n", "        f.write(analysis_text)\n", "    \n", "    print(f\"✅ Analysis exported to {filename}\")\n", "\n", "# Example: Export the last analysis\n", "# export_analysis(result, 'my_analysis_report.txt')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instructions for Use\n", "\n", "### Setup:\n", "1. **Get Together.ai API Key**: \n", "   - Go to https://www.together.ai/\n", "   - Create an account and get your API key\n", "   - Replace `\"your_together_api_key_here\"` with your actual API key\n", "\n", "2. **Install Dependencies**: Run the first cell to install all required packages\n", "\n", "3. **Initialize Agent**: Run the configuration and agent initialization cells\n", "\n", "### Usage:\n", "1. **Upload Files**: Use `agent.upload_and_analyze(file_path)` or `agent.upload_and_analyze(file_path, question)`\n", "2. **Ask Questions**: Use `agent.ask_followup(question)` for follow-up questions\n", "3. **Interactive Mode**: Uncomment and run `interactive_analysis()` for guided analysis\n", "\n", "### Supported File Types:\n", "- **CSV/Excel**: Full analysis with visualizations\n", "- **Text/Word/PDF**: Content analysis and Q&A\n", "- **Images**: Basic image information and analysis\n", "\n", "### Features:\n", "- ✅ Multi-format file support\n", "- ✅ AI-powered analysis using Llama-4-Maverick\n", "- ✅ Automatic visualizations\n", "- ✅ Follow-up questions\n", "- ✅ Interactive mode\n", "- ✅ Export functionality\n", "\n", "**Note**: Make sure to replace the API key before running the notebook!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}