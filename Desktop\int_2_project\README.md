# Data Analyst Agent

A comprehensive data analysis agent powered by **Llama-4-Maverick-17B-128E-Instruct-FP8** from Together.ai.

## Features

- 📁 **Multi-format file support**: CSV, Excel, TXT, DOCX, PDF, Images
- 🤖 **AI-powered analysis**: Using the specified Llama model from Together.ai
- 📊 **Automatic visualizations**: Charts, plots, and interactive graphs
- ❓ **Q&A capability**: Ask questions about your data
- 🔄 **Follow-up questions**: Continue the conversation about your data
- 📋 **Export results**: Save analysis reports

## Setup Instructions

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Get Together.ai API Key
1. Go to [Together.ai](https://www.together.ai/)
2. Create an account
3. Get your API key from the dashboard
4. Replace `"your_together_api_key_here"` in the notebook with your actual API key

### 3. Run the Notebook
```bash
jupyter notebook data_analyst_agent.ipynb
```

## Usage

### Basic Analysis
```python
# Initialize the agent
agent = DataAnalystAgent(TOGETHER_API_KEY, MODEL_NAME)

# Analyze a file
result = agent.upload_and_analyze("your_file.csv")
print(result)
```

### Ask Specific Questions
```python
# Analyze with a specific question
result = agent.upload_and_analyze("your_file.csv", "What are the main trends?")
print(result)
```

### Follow-up Questions
```python
# Ask follow-up questions
followup = agent.ask_followup("Are there any outliers in the data?")
print(followup)
```

### Interactive Mode
```python
# Run interactive analysis
interactive_analysis()
```

## Supported File Types

- **CSV/Excel (.csv, .xlsx, .xls)**: Full data analysis with statistics and visualizations
- **Text files (.txt)**: Content analysis and text insights
- **Word documents (.docx)**: Document analysis and Q&A
- **PDF files (.pdf)**: Text extraction and analysis
- **Images (.jpg, .png, etc.)**: Basic image information

## Model Requirement

⚠️ **Important**: This project uses the specific model `meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8` from Together.ai as required. Using any other model will disqualify the submission.

## Example Output

The agent provides:
- Data summaries and statistics
- Key insights and patterns
- Data quality observations
- Visualization recommendations
- Answers to specific questions
- Follow-up analysis capabilities

## Files

- `data_analyst_agent.ipynb`: Main Jupyter notebook with the complete agent
- `requirements.txt`: Python dependencies
- `README.md`: This documentation file

## Notes

- The notebook includes sample data generation for testing
- All visualizations are automatically created for structured data
- The agent maintains conversation context for follow-up questions
- Analysis results can be exported to text files
